#!/usr/bin/env python3
"""
DataFrame Merging Script - Simplified and Robust Version
========================================================

This script merges DataFrames by removing error rows from old_results 
and replacing them with corrected data from updated_results.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def main():
    """Main execution function"""
    print("=" * 80)
    print("DATAFRAME MERGING OPERATION")
    print("=" * 80)
    
    # Step 1: Load DataFrames
    print("\n1. Loading DataFrames...")
    try:
        updated_results = pd.read_excel("results_evals.xlsx", sheet_name="Results")
        old_results = pd.read_excel("results_evals.xlsx", sheet_name="Appended")
        print(f"   ✓ updated_results: {updated_results.shape}")
        print(f"   ✓ old_results: {old_results.shape}")
    except Exception as e:
        print(f"   ❌ Error loading data: {e}")
        return
    
    # Step 2: Identify error rows
    print("\n2. Identifying error rows...")
    error_patterns = ['error', 'Error', 'ERROR', 'exception', 'Exception', 'failed', 'Failed']
    error_mask = pd.Series([False] * len(old_results))
    
    for pattern in error_patterns:
        pattern_mask = old_results['reason'].astype(str).str.contains(pattern, na=False)
        error_mask = error_mask | pattern_mask
    
    error_count = error_mask.sum()
    print(f"   ✓ Error rows found: {error_count} ({error_count/len(old_results)*100:.2f}%)")
    
    # Step 3: Prepare matching strategy
    print("\n3. Preparing matching strategy...")
    
    # Add paper_id to old_results error rows
    error_rows = old_results[error_mask].copy()
    error_rows['paper_id'] = error_rows['extraction_id'].str.extract(r'(W\d+)')
    
    # Find paper IDs that exist in both datasets
    error_paper_ids = set(error_rows['paper_id'].dropna())
    updated_paper_ids = set(updated_results['paper_id'])
    matching_paper_ids = error_paper_ids & updated_paper_ids
    
    print(f"   ✓ Paper IDs in error rows: {len(error_paper_ids)}")
    print(f"   ✓ Paper IDs in updated_results: {len(updated_paper_ids)}")
    print(f"   ✓ Matching paper IDs: {len(matching_paper_ids)}")
    
    # Step 4: Perform matching
    print("\n4. Performing matching...")
    
    # Filter to matchable rows
    matchable_errors = error_rows[error_rows['paper_id'].isin(matching_paper_ids)]
    matchable_updated = updated_results[updated_results['paper_id'].isin(matching_paper_ids)]
    
    print(f"   ✓ Matchable error rows: {len(matchable_errors)}")
    print(f"   ✓ Matchable updated rows: {len(matchable_updated)}")
    
    # Create matching keys using multiple attributes
    matching_cols = ['paper_id', 'adc_name', 'model_name', 'endpoint_name', 'experiment_type', 'model_type']
    
    # Create composite keys
    error_keys = matchable_errors[matching_cols].apply(lambda x: tuple(x), axis=1)
    updated_keys = matchable_updated[matching_cols].apply(lambda x: tuple(x), axis=1)
    
    # Find matches
    matched_keys = set(error_keys) & set(updated_keys)
    print(f"   ✓ Successful matches: {len(matched_keys)}")
    
    # Step 5: Create merged DataFrame
    print("\n5. Creating merged DataFrame...")
    
    if len(matched_keys) > 0:
        # Get matched rows from updated_results
        updated_key_series = matchable_updated[matching_cols].apply(lambda x: tuple(x), axis=1)
        matched_updated_mask = updated_key_series.isin(matched_keys)
        matched_updated_rows = matchable_updated[matched_updated_mask].copy()
        
        # Get indices of error rows to remove
        error_key_series = matchable_errors[matching_cols].apply(lambda x: tuple(x), axis=1)
        matched_error_mask = error_key_series.isin(matched_keys)
        error_indices_to_remove = matchable_errors[matched_error_mask].index
        
        # Remove matched error rows from old_results
        clean_old_results = old_results.drop(error_indices_to_remove)
        
        # Prepare updated rows for merging
        matched_updated_rows = prepare_for_merge(matched_updated_rows, old_results)
        
        # Merge
        final_merged = pd.concat([clean_old_results, matched_updated_rows], ignore_index=True)
        
        print(f"   ✓ Removed error rows: {len(error_indices_to_remove)}")
        print(f"   ✓ Added corrected rows: {len(matched_updated_rows)}")
        print(f"   ✓ Final shape: {final_merged.shape}")
        
        # Step 6: Validate
        print("\n6. Validating merged data...")
        validation_passed = validate_data(final_merged, old_results, len(error_indices_to_remove), len(matched_updated_rows))
        
        # Step 7: Export
        print("\n7. Exporting to Excel...")
        export_filename = export_data(final_merged, old_results.shape, len(error_indices_to_remove), len(matched_updated_rows), len(matched_keys))
        
        # Step 8: Summary
        print("\n8. Summary Report...")
        print_summary(old_results.shape, len(error_indices_to_remove), len(matched_updated_rows), len(matched_keys), final_merged.shape, matching_cols, validation_passed, export_filename)
        
    else:
        print("   ❌ No matches found - cannot proceed with merge")

def prepare_for_merge(updated_rows, old_results):
    """Prepare updated rows for merging by aligning columns"""
    # Rename paper_id to id
    if 'paper_id' in updated_rows.columns:
        updated_rows = updated_rows.rename(columns={'paper_id': 'id'})
    
    # Add missing columns
    for col in old_results.columns:
        if col not in updated_rows.columns:
            if col == 'expert_opinion':
                updated_rows[col] = True
            elif col == 'reason':
                updated_rows[col] = 'Corrected in updated iteration'
            else:
                updated_rows[col] = None
    
    # Remove extra columns
    extra_cols = set(updated_rows.columns) - set(old_results.columns)
    if extra_cols:
        updated_rows = updated_rows.drop(columns=list(extra_cols))
    
    # Reorder columns
    return updated_rows[old_results.columns]

def validate_data(final_merged, old_results, errors_removed, corrections_added):
    """Validate the merged data"""
    checks_passed = 0
    total_checks = 4
    
    # Check 1: Row count
    expected_rows = len(old_results) - errors_removed + corrections_added
    if len(final_merged) == expected_rows:
        print("   ✓ Row count validation passed")
        checks_passed += 1
    else:
        print(f"   ❌ Row count mismatch: expected {expected_rows}, got {len(final_merged)}")
    
    # Check 2: Column structure
    if final_merged.shape[1] == old_results.shape[1]:
        print("   ✓ Column count validation passed")
        checks_passed += 1
    else:
        print("   ❌ Column count mismatch")
    
    # Check 3: No duplicates
    duplicates = final_merged.duplicated().sum()
    if duplicates == 0:
        print("   ✓ No duplicates found")
        checks_passed += 1
    else:
        print(f"   ⚠️  {duplicates} duplicates found")
    
    # Check 4: Error removal
    remaining_errors = final_merged['reason'].astype(str).str.contains('error|Error|ERROR', na=False).sum()
    if remaining_errors == 0:
        print("   ✓ All error rows removed")
        checks_passed += 1
    else:
        print(f"   ⚠️  {remaining_errors} error rows still present")
    
    print(f"   Validation: {checks_passed}/{total_checks} checks passed")
    return checks_passed == total_checks

def export_data(final_merged, original_shape, errors_removed, corrections_added, successful_matches):
    """Export the merged data to Excel"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"merged_results_{timestamp}.xlsx"
    
    try:
        with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
            # Main data
            final_merged.to_excel(writer, sheet_name='Merged_Results', index=False)
            
            # Summary
            summary_data = {
                'Metric': ['Original Rows', 'Error Rows Removed', 'Corrected Rows Added', 
                          'Final Rows', 'Successful Matches', 'Export Timestamp'],
                'Value': [original_shape[0], errors_removed, corrections_added, 
                         final_merged.shape[0], successful_matches, 
                         datetime.now().strftime("%Y-%m-%d %H:%M:%S")]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='Summary', index=False)
        
        print(f"   ✓ Exported to: {filename}")
        return filename
    except Exception as e:
        print(f"   ❌ Export failed: {e}")
        return None

def print_summary(original_shape, errors_removed, corrections_added, successful_matches, final_shape, matching_cols, validation_passed, export_filename):
    """Print comprehensive summary"""
    print("\n" + "=" * 80)
    print("📊 FINAL SUMMARY REPORT")
    print("=" * 80)
    print(f"📈 STATISTICS:")
    print(f"   • Original rows: {original_shape[0]}")
    print(f"   • Error rows removed: {errors_removed}")
    print(f"   • Corrected rows added: {corrections_added}")
    print(f"   • Successful matches: {successful_matches}")
    print(f"   • Final rows: {final_shape[0]}")
    print(f"   • Net change: {final_shape[0] - original_shape[0]}")
    
    print(f"\n🔍 MATCHING:")
    print(f"   • Strategy: Paper ID + attribute matching")
    print(f"   • Fields used: {matching_cols}")
    print(f"   • Success rate: {successful_matches/errors_removed*100:.1f}%")
    
    print(f"\n✅ VALIDATION:")
    print(f"   • Status: {'PASSED' if validation_passed else 'NEEDS REVIEW'}")
    
    print(f"\n💾 EXPORT:")
    print(f"   • File: {export_filename if export_filename else 'FAILED'}")
    
    print(f"\n🎉 OPERATION COMPLETED SUCCESSFULLY!")
    print("=" * 80)

if __name__ == "__main__":
    main()
