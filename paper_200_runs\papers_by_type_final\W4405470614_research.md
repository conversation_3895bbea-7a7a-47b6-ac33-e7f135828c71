# QSP modeling of a transiently inactivating antibody-drug conjugate highlights benefit of short antibody half life

## Abstract
<!-- PARAGRAPH_START [abstract] -->Antibody drug conjugates (ADC) are a promising class of oncology therapeutics consisting of an antibody conjugated to a payload via a linker. DYP688 is a novel ADC comprising of a signaling protein inhibitor payload (FR900359) that undergoes unique on-antibody inactivation in plasma, resulting in complex pharmacology. To assess the impact of FR inactivation on DYP688 pharmacology and clinical developability, we performed translational modeling of preclinical PK and tumor growth inhibition (TGI) data, accompanied by mechanistic Krogh cylinder tumor modeling. Using a PK-TGI model, we identified a composite exposure-above-tumorostatic concentration (AUCTSC) metric as the PK-driver of efficacy. To underpin the mechanisms behind AUCTSC as the driver of efficacy, we performed quantitative systems pharmacology (QSP) modeling of DYP688 intratumoral pharmacokinetics and pharmacodynamics. Through exploratory simulations, we show that by deviating from canonical ADC design dogma, DYP688 has optimal FR900359 activity despite its transient inactivation. Finally, we performed the successful preclinical to clinical translation of DYP688 PK, including the payload inactivation kinetics, evidenced by good agreement of the predicted PK to the observed interim clinical PK. Overall, this work highlights early quantitative pharmacokinetics as a missing link in the ADC design-developability chasm.

<!-- PARAGRAPH_START [abstract] -->The online version contains supplementary material available at 10.1007/s10928-024-09956-1.

## Supplementary Information

<!-- PARAGRAPH_START [direct] -->The online version contains supplementary material available at 10.1007/s10928-024-09956-1.

## Introduction

<!-- PARAGRAPH_START [direct] -->Antibody-drug conjugates (ADC) are a complex class of oncology therapeutics that have gained exponential clinical success in the last decade, particularly against solid tumors. The earliest generations of ADC designs focused on selectively delivering conventional chemotherapeutics (e.g., doxorubicin [1]) via conjugation to tumor-antigen targeting antibodies. However, the poor intrinsic potency of such agents [2] ushered in the subsequent generation of ADC designs that utilize more potent cytotoxic payloads (primarily microtubule inhibitors, topoisomerase inhibitors, and DNA interacting payloads) [3]. Pivotal clinical success from this transition led to a greater focus of innovation on the protein backbone and linker features, and little deviation in payloads beyond cytotoxins. However, with the exploration of non-cytotoxic payloads such as disease pathway inhibitors [4] and protein degraders [5], the emerging next generation of ADCs is poised to exploit a “targeted-targeted” approach, where both the protein backbone and delivered payload target the disease features to selectively trigger anti-cancer activity (versus non-cell specific cytotoxicity), thus promising to further expand the ADC therapeutic index.

<!-- PARAGRAPH_START [direct] -->Unlike with cytotoxic payloads where a threshold exposure is often sufficient to trigger irreparable cell death [6, 7], targeted payloads may require alternative pharmacokinetic (PK) and pharmacodynamic (PD) properties to sufficiently target and inhibit the complex disease pathway. Consequently, such ADCs require a deeper PK/PD understanding beyond empirical exposure-response analysis. To illustrate this, we present the quantitative modeling of DYP688, a novel “targeted-targeted” antibody-drug conjugate developed against mutant uveal melanoma (UVM), comprising an anti-PMEL17 monoclonal antibody conjugated to FR900359 (FR) [8], a cyclic depsipeptide that selectively and potently inhibits Gαq/11 (encoded by GNAQ/GNA11 gene). Gα (a subunit of heterotrimeric G proteins) acts as molecular “on-off” switches for G-protein coupled receptor (GPCR)-mediated signaling by shuttling between inactive GDP- and active GTP- bound states [9]. Uveal melanoma is commonly characterized by activating mutations that lock Gαq/11 in a GTP “on” state, promoting the growth and survival of UVM cells [10]. FR inhibits the activity of Gαq/11 by locking it in its inactive GDP state and inhibiting downstream growth signal transduction. Like GNAQ/11 mutations, another unique feature of UVM is the expression of PMEL17, a melanosome-specific protein with considerable surface expression in melanoma tumors, but restricted normal tissue expression, making it an ideal target for ADCs [11].

<!-- PARAGRAPH_START [direct] -->Preclinical evaluation of DYP688 pharmacology highlighted unique payload biotransformation dynamics not previously observed with ADCs. Unlike most other payloads, FR undergoes metabolic biotransformation while conjugated to the antibody, but only in plasma and not in tumors. This results in the formation of ADC species carrying inactive payload that does not contribute to anti-cancer activity. Interestingly, unconjugated FR has previously displayed ring-opening ester hydrolysis under basic pH conditions and metabolic instability in mouse/human liver microsomes [8]. To assess the impact of FR biotransformation on in vivo pharmacology and clinical developability, we performed translational modeling of DYP688 preclinical pharmacokinetic (PK) and tumor growth inhibition (TGI) data, accompanied by mechanistic tumor pharmacology modeling. In this work, we (1) quantify and translate preclinical DYP688 pharmacokinetics, including FR inactivation, to the anticipated PK in humans, (2) identify the PK driver of DYP688 efficacy from preclinical data, and (3) perform mechanistic Krogh cylinder modeling of DYP688 to extrapolate lessons on the ADC design-exposure-response relationship.

## Methods

### Preclinical in vivo study design

<!-- PARAGRAPH_START [direct] -->Mice were maintained and handled in accordance with the license number BS-2712 approved by the Cantonal Veterinary Office of Basel Stadt. Xenograft studies were performed with female nude Crl: NU(NCr)-Foxn1nu-Homozygous mice (Charles River, Germany), at 6 to 8 weeks of age. The animals were kept under OHC conditions in Allentown mice cages (maximum of 5 animals/cage) with a 12-hour dark, 12-hour light conditions (lights on: 6 AM, lights off: 6 PM). The animals were fed sterilized food and water ad libitum. 92.1 luc xenograft studies were performed with xenograft mice that were established by subcutaneous injection of 5 × 106 cells in 200 µL HBSS (Sigma, #H8264):Matrigel (1:1) into the right flank of nude mice. Tumor growth was monitored regularly post cell inoculation and animals were randomized into treatment groups (n = 4–6) when the mean tumor volumes were approximately 150–400 mm3. During the treatment period, tumor volume was measured about twice a week. Tumor volume (mm3) was calculated from (L x W2 x π/6) where W = width and L = length of the tumor. However, at maximum efficacy in the 92.1-luc model, the tumors became flat with only an ovoid dark melanin colored patch remaining. Consequently, for a better estimate of the tumor volume the formula (L x W x H x π/6) was used. The height (H) of the tumor was difficult to measure reliably, so it was set to 1 mm. Total mAb and total inactive ADC mouse serum concentrations of DYP688 were measured using a sequential sandwich ELISA, while total active ADC was measured in serum using a homogeneous sandwich ELISA, unless otherwise specified (described below).

<!-- PARAGRAPH_START [direct] -->For a GLP toxicology study in non-human primates (NHP), DYP688 was administered as a slow IV bolus to male cynomolgus monkeys at 30 and 75 mg/kg every two weeks up to Day 71 (total of six administrations). Exposure following the first administration on Day 1 and the sixth administration on Day 71 were compared for males and females at the two different dose levels. After these two dosing occasions, blood was collected at 7 time points over 14 days (pre-dose and 1, 6, 24, 72, 168 and 336 h post-dose). Blood samples were split, with one aliquot processed to serum and the other to plasma. Total mAb NHP serum concentrations of DYP688 were measured using a sequential sandwich ELISA (LLOQ of 0.144 µg/mL), while conjugated active payload and conjugated inactive payload were measured in plasma using a PRA/LC-MS assay (described below).

### In vitro plasma stability assessment

<!-- PARAGRAPH_START [direct] -->DYP688 was spiked at 100 µg/mL in pooled mouse, NHP, and human plasma each and incubated at 37 °C for 0, 6, 24, 48, 72, 96, and 168 h. At each time point, samples were removed and stored in the freezer (-65 to -90 °C) pending analysis. A papain release assay (PRA) was used to determine conjugated active and inactive payloads for the NHP and human samples. Two different ligand binding assays (LBA) were used to determine the concentration of ADCs bearing active and inactive payloads for the mouse samples.

### Pharmacokinetic assays

<!-- PARAGRAPH_START [direct] -->A sequential sandwich ELISA assay (LBA) was developed to quantify the total antibody, total active ADC, and total inactive ADC concentration of the drug. The assay is based on capture of drug present in standards, quality controls, and study samples with biotinylated goat polyclonal antibody directed against human IgG (for total antibody)/ biotinylated anti-active-payload Fab directed against active drug (for total active ADC)/ biotinylated anti-inactive-payload polyclonal IgG directed against inactive drug (for total inactive ADC) that is coated on the streptavidin plate. Bound drug is then detected with a goat polyclonal anti-human-IgG antibody coupled to HRP (for total antibody)/ mouse monoclonal anti-human-IgG Fc antibody coupled to HRP (for total active ADC and total inactive ADC), with TMB serving as the substrate. A homogeneous sandwich ELISA assay, which follows the same general protocol as the sequential sandwich ELISA assay, was also developed to quantify the total active ADC concentration of the active drug.

<!-- PARAGRAPH_START [direct] -->For the papain-release assay (PRA) to determine concentrations of conjugated active or inactive payload, all preparations were made using LoBind tubes or LoBind 96-well plates on wet ice. Standard curve, quality control, and study samples were treated as follows. Briefly, an aliquot of 50 µL of each plasma sample was placed into a 96-well plate. Samples were protein precipitated by adding 200 µL of 0.5% acetic acid in acetonitrile/methanol (90/10, v/v). The plate was sealed, vortex mixed at 1650 rpm for ~ 3 min and centrifuged at 2500 g for 10 min at room temperature. The protein precipitate was washed by adding 200 µL of acetonitrile/water (75/25, v/v) to each pellet. The plate was sealed, vortex mixed at 1800 rpm for 3 min and centrifuged at 4000 rpm for 3 min. The supernatant was discarded, and the wash procedure was repeated two additional times. The plate containing the washed pellet was dried using a vacuum centrifuge. A papain solution was prepared at a concentration of 2 mg/mL in a buffer containing 20 mM potassium phosphate, 10 mM EDTA at pH 7 and was incubated at 37 °C for 15 min to activate the papain prior to use. The dried pellets were reconstituted in 100 µL of the activated papain solution and were re-suspended by vortex mixing the plate at 2000 rpm for 3 min. The plate was then incubated at 37 ºC overnight with gently shaking at 500 rpm. After incubation, 300 µL of acetonitrile containing 5 ng/mL of the internal standard tolbutamide was added to each sample, the plate was sealed, vortex mixed at 1650 rpm for 3 min, and centrifuged at 4000 rpm for 10 min. Aliquots of 100 µL were transferred to a fresh 96-well plate and 100 µL of water was added to each sample. The resulting samples were analyzed by LC-MS/MS to determine the levels of released active and inactive conjugated payloads.

<!-- PARAGRAPH_START [direct] -->The same detection assay set-ups were employed for both in vivo and in vitro samples. Employed reagents for all assays are listed in Supplementary Table 3.

### Pharmacodynamic assay

<!-- PARAGRAPH_START [direct] -->Dissected 92.1 xenograft tumor samples were fixed in 10% (v/v) neutral buffered formalin for 24 h at room temperature, rinsed in PBS, dehydrated in ascending baths of ethanol, cleared in xylene, and finally embedded in paraffin. Paraffin sections (3 μm) were cut on a rotary microtome, spread in a 45 °C water-bath, mounted on microscope slides Superfrost Plus™ and air-dried in an oven at 60 °C for 20mn. Formalin-fixed paraffin-embedded section was systematically stained for Hematoxylin & Eosin (HE) staining. The serial following section was then stained on a Leica Bond RX™ per manufacturer’s instructions, using Bond Polymer Refine detection kit with Epitope Retrieval 2 (ER2) conditions for 20 min at 100 °C as pretreatment conditions. Phospho-p44/42 MAPK (Erk1/2) (Thr202/Tyr204) Rabbit Monoclonal antibody (clone D13.14.4E) was diluted at optimal concentration in Bond Primary Antibody diluent and was incubated for one hour at room temperature. After staining, slides were counterstained with Hematoxylin, dehydrated, and cover slipped with Pertex mounting medium. Dried slides were digitalized with Aperio ScanScope XT slide scanner. Phospho Erk1,2 (pERK) expression quantification was performed using HALO (Indica Labs) Cytonuclear IHC algorithm that measures cell by cell IHC positivity for single stain application. Each cell is measured for nuclear and or cytoplasm positivity of every individual stain. Results are represented as the H Score for each animal, incorporating both the staining intensity and a percentage of stained cells at each intensity level. H score is automatically calculated by the HALO software. Employed reagents and instruments are listed in Supplementary Table 3.

### Clinical study design

<!-- PARAGRAPH_START [direct] -->Pharmacokinetics of DYP688 was evaluated in an ongoing Phase I, single-arm, dose-escalation study in patients with metastatic uveal melanoma and other GNAQ/11 mutant melanomas (NCT05415072). DYP688 was administered as an intravenous infusion at 4, 8, 12 or 16 mg/kg biweekly (q2w), in 4-week cycles. Intensive PK sampling was collected after first dose and on cycle 3, and sparse samples were collected up to the fifth cycle of treatment. Validated bioanalytical assays were used for quantification of total antibody in serum (LBA), active and inactive conjugated payload in plasma (PRA, LC-MS) and free payload in blood (LC-MS).

### Theoretical

#### DYP688 mouse and NHP PK modeling

<!-- PARAGRAPH_START [direct] -->A two-compartment model with linear clearance (kel) was used to describe the PK of the total ADC (Fig. 1A). The ADC was described to have two conjugated FR payload moieties, each of which can non-cooperatively undergo inactivation while attached to the antibody in the central (Vc) and the peripheral compartment (Vp). Thus, the model accounts for three ADC forms – ADC with both active payload, ADC with only one active payload, and ADC with not active payloads. Note, the inactivation of each payload is parametrized as ki.a.. However, for ADC with both active payloads (ADC2P), each of the active payloads can non-cooperatively inactivate to form ADC with one active payload (ADC1P). To account for this non-cooperative inactivation, the conversion of ADC2P to ADC1P is parametrized as 2\*ki.a. in the model structure. Each of the ADC species can distribute between the central and peripheral compartments with the same kinetics (k12, k21). For model simplification, deconjugation of the active FR payload was considered negligible relative to payload inactivation. Correspondingly, the release of free payload into circulation was also not considered. Total antibody was assumed to be the sum of all three forms of the ADC to maintain mass balance.

<!-- PARAGRAPH_START [direct] -->Fig. 1PK-TGI model schematic employed for DYP688. (A) Two-compartment, linear clearance model with non-cooperative inactivation of the attached payload captures the PK of three ADC species in circulation. (B) Total conjugated active payload (calculated as ADC species concentration \documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{amsbsy}
\usepackage{mathrsfs}
\usepackage{upgreek}
\setlength{\oddsidemargin}{-69pt}
\begin{document}$$\:\times\:$$\end{document} DAR of active payload per ADC species) drives tumor growth (Gompertz model) inhibition using a linear killing function and signal distribution model consisting of one transit compartment

<!-- PARAGRAPH_START [direct] -->For reliable calibration of the mouse PK model, it was assumed that the ADC form with one active and one inactive payload (ADC1P) is detected by both the active ADC (“active”) Ligand Binding Assay (LBA) and inactive ADC (“inactive”) LBA. To that end, the “active” LBA was assumed to detects predominantly the ADC form with two active payloads (ADC2P) along with a fraction (pinact) of ADC1P, while the “inactive ADC” LBA detects predominantly the ADC form with no active payloads (ADC0P) along with a fraction (pinact2) of ADC1P. Both pinact and pinact2 were estimated during model calibration, and not required for calibrating NHP active and inactive ADC data as the LC/MS assay (PRA) detects absolute payload concentration. PRA payload release was assumed to be 100%, corresponding to internal validation controls.

<!-- PARAGRAPH_START [direct] -->Finally, the model accounted for a small fraction of inactive ADC in the vial (fr) to account for any batch-to-batch formulation variability, with 99% of the inactive ADC in the dose was assumed to be in the ADC0P form (i.e., both payloads inactive).

#### NHP to human PK scaling using allometry and in vitro characterization

<!-- PARAGRAPH_START [direct] -->Cynomolgus monkey was determined as a suitable species for allometric scaling based on similar DYP688 affinity (24 nM vs. 65 nM) and apparent avidity (0.2 nM vs. 0.1 nM) for human PMEL17 and NHP PMEL17, respectively. Human plasma/serum volume of distribution (Vc), peripheral volume of distribution (Vp), and elimination rate (kel) were allometrically scaled from corresponding NHP PK parameters by body weight with an exponent w = 1, as described for linear PK ADCs [12]. Human and cynomolgus monkey weight were assumed to be 70 kg and 3 kg respectively. Human payload inactivation rate (ki.a.) was assumed the same as NHP, based on the comparison of in vitro plasma stability between the two species. Intercompartment distribution rate constants (k12, k21) were assumed the same as NHP. Initial fraction of inactive ADC (fr) was set constant, as the NHP study was performed using GLP manufactured material. Scaled human PK parameters were used to simulate the first-in-human (FiH) dose escalation study PK.

#### DYP688 mouse PK-TGI modeling and simulation

<!-- PARAGRAPH_START [direct] -->Mouse 92.1 cell-derived xenograft (CDX) tumor growth inhibition was modeled using existing models in the Monolix library (Fig. 1B). Briefly, CDX tumor growth kinetics were calibrated to the saline-treated (negative control) group using a Gompertz tumor growth model. Tumor killing (killing rate constant, kkill) in DYP688 treated groups was assumed to be linearly proportional to conjugated active payload in plasma. Drug effect was assumed to occur a signal distribution cascade model with one transit compartment (transit time, 𝜏). All PK parameters and corresponding variability were constrained to those established using the mouse PK model. The final calibrated PK-TGI model simulated for validation against an independent study and for exploration of the correlation between tumor growth inhibition and various plasma PK metrics such as Cmax and exposure.

#### Mechanistic Krogh modeling of DYP688 tumor uptake and FR-Gαq/11 binding kinetics

<!-- PARAGRAPH_START [direct] -->The above popPK-TGI modeling described a bespoke empirical relationship between plasma PK and tumor response. However, the tumor PK of ADCs (particularly the delivered payload) is often more complex than indicated by the plasma PK [13, 14] a feature that can critically influence the exposure-response relationship. This can be particularly important for a targeted payload like FR which specifically inhibits a critical cell survival protein to trigger apoptosis. To characterize the missing link of tumor payload delivery and intracellular target engagement, we performed mechanistic modeling using the previously validated Krogh cylinder framework [15], tailored to DYP688 and FR-Gαq/11 binding kinetics (Supplementary Equations, Supplementary Table 1).

<!-- PARAGRAPH_START [direct] -->Briefly, the PK of the three ADC forms (ADC2P, ADC1P, and ADC0P) was used as input for a one-dimensional Krogh cylinder model (i.e., no axial gradients due to permeability-limited uptake of ADCs). Each ADC independently extravasates across the blood vessel (PADC), diffuses through the tumor interstitium (Deff, ADC), and competitively binds (kon, ADC, koff, ADC) to the same pool of surface expressed PMEL17 (Ag0). Free target is internalized (ke) into the cell and recycled back (Rs) to maintain steady state surface PMEL17 expression. Bound ADC is internalized (ke) into the lysosome at the same rate as free target, where linker cleavage (krel) and antibody degradation (kdeg) releases active FR molecules proportional to the active FR drug-to-antibody ratio (DAR) of each ADC form. Inactive FR is assumed to be inert (i.e., no Gαq/11 binding competition with active FR). ADCs with inactive payload are considered functionally equivalent to corresponding species with no payload and modeled as one pool (e.g. unconjugated antibody formed from the release of both active payloads is considered in the same pool as ADC with two inactive payloads). Thus, for model simplicity, only active FR kinetics are modeled here. Active FR irreversibly escapes the lysosome (kin, P) into the cytoplasm, where it reversibly binds to Gαq/11 (kon, P, koff, P). Payload can also efflux (kout, P) and influx (kin, P) across the cell membrane. Intracellular Gαq/11 turnover is also included. For simplification, GDP to GTP cycling of Gαq/11 was not modeled here given (1) 80% of Gαq is estimated to exist in the GDP form [16], the predominant species for FR binding and (2) the rapid rate of phosphorylation and hydrolysis [17] relative to FR-Gαq/11 binding residence time. Payload transport parameters (Supplementary Table 1) were estimated based on literature values and theoretical calculations described previously [15].

#### Software

<!-- PARAGRAPH_START [direct] -->Population PK/TGI modeling and simulations were performed using the Monolix Suite v2020 (Lixoft, Paris, France). Krogh model simulations were performed using MATLAB R2020b (MathWorks, Natick, MA). Data analysis and visualization were performed using RStudio and Prism v9 (GraphPad, San Diego, CA). DYP688 clinical pharmacokinetic metrics were computed using non-compartmental analysis methods in Phoenix WinNonlin™ version 8.3 (Pharsight, Mountain View, USA).

## Results

### Modeling with payload inactivation captures mouse and NHP PK

<!-- PARAGRAPH_START [direct] -->The mouse PK model was collectively fit (Fig. 2A) to the active ADC, inactive ADC, and total antibody (mAb) for a total of six treatment groups: two single dose DYP688 treatment groups (3 mg/kg and 6 mg/kg), and two sets of dose fractionation groups (1.5 mg/kg Q2W & 3 mg/kg Q4W; 3 mg/kg Q2W & 6 mg/kg Q4W). The PK model was estimated with satisfactory accuracy (Table 1, with relative standard errors of all estimates < 50%). The model captures the total mAb and active ADC in an overall unbiased manner, with only a slight underestimation of the total mAb at 6 mg/kg (Figure S1 – S2) and slight overestimation of the inactive ADC in nearly all treatment groups. The NHP PK model was fit to the conjugated active payload, conjugated inactive payload, and total mAb for a total of two single dose treatment groups: 30 mg/kg and 75 mg/kg (Fig. 2B; Table 2). As with the mouse PK, the total mAb and conjugated active payload (“active ADC”) are well captured in an unbiased manner by the PK model. However, the conjugated inactive payload (“inactive ADC”) is overestimated by the model, though the discrepancy is much larger in NHPs than in mice.

<!-- PARAGRAPH_START [direct] -->Fig. 2Population PK fitting of total antibody, active ADC, and inactive ADC in (A) mouse plasma, (B) NHP plasma. (C) In vitro stability and mass balance assessment in mouse, NHP, and human plasma

<!-- PARAGRAPH_START [direct] -->Table 1pop-PK parameters inferred from mice for DYP688

| Parameter | Notation (unit) | Estimate | RSE (%) | IISD | IISD RSE (%) |
| --- | --- | --- | --- | --- | --- |
| Antibody/ADC elimination rate | kel (1/h) | 0.016 | 12.1 | 0.45 | 28.6 |
| Plasma to periphery distribution rate | k12 (1/h) | 0.035 | 11.4 | 0.15 | - |
| Periphery to plasma distribution rare | k21 (1/h) | 0.036 | 14.1 | 0.26 | - |
| Payload inactivation rate | ki.a. (1/h) | 0.013 | 2.77 | 0.075 | 38.6 |
| Plasma volume of distribution | Vc (mL) | 1.53 | 2.5 | 0.03 | - |
| Fraction ADC with 1 active payload detected in active assay | pinact | 0.79 | 9.33 | - | - |
| Fraction ADC with 1 inactive payload detected in inactive assay | pinact2 | 0.16 | 24.5 | - | - |
| Fraction of inactive ADC in dose | fr | 0.027 | 0.46 | - | - |
| Additive lognormal measurement error model parameter for Total mAb | a (nM) | 0.41 | 8.67 | - | - |
| Additive lognormal measurement error model parameter for Active ADC | a (nM) | 0.24 | 7.73 | - | - |
| Additive lognormal measurement error model parameter for Inactive ADC | a (nM) | 0.52 | 8.24 | - | - |
| Additive and multiplicative normal measurement error model parameter for % conjugated payload | a (%), b | 0.65, 0.21 | 28.5, 10.3 | - | - |

<!-- PARAGRAPH_START [direct] -->Table 2pop-PK parameters inferred from NHP for DYP688

| Parameter (unit) | Notation (unit) | Estimate | RSE (%) | IISD | IISD RSE (%) |
| --- | --- | --- | --- | --- | --- |
| Plasma to periphery distribution rate | k12 (1/h) | 0.064 | 0.288 | - | - |
| Periphery to plasma distribution rate | k21 (1/h) | 0.039 | 11.8 | 0.48 | - |
| Payload inactivation rate | ki.a. (1/h) | 0.0114 | 1.62 | 0.06 | 22.1 |
| Antibody/ADC elimination rate | kel (1/h) | 0.013 | - | - | - |
| Plasma volume of distribution | Vc (L) | 0.083 | 4.83 | 0.21 |  |
| Multiplicative measurement error parameter for Total mAb | b | 0.37 | - |  |  |
| Multiplicative measurement error parameter for conj. active payload | b | 0.27 | 5.51 |  |  |
| Multiplicative measurement error parameter for conj. inactive payload | b | 0.73 | 4.26 |  |  |

Fraction of inactive ADC in dose (fr) vial assumed to 1%

<!-- PARAGRAPH_START [direct] -->To further examine the source of discrepancy in the inactive ADC/conjugated inactive payload PK fitting, we evaluated the in vitro stability of DYP688 in mouse (LBA), NHP (PRA), and human plasma (PRA) (Fig. 2C). Since there is no elimination, only biotransformation in vitro, the mass balance of the total drug should be maintained i.e., total mAb/ADC should be a sum of the active ADC and inactive ADC. However, as is evident from the data, the mass balance is not conserved throughout the duration of the study. Specifically, the sum of active and inactive ADC corresponds to the amount of initial DYP688 spiked into the plasma up to ~ 48 h, but not beyond that. This suggests that additional species of the inactive ADC are formed, which cannot be detected by LBA and PRA assays developed against a specific molecular species of inactive payload (“IP-1”). Additionally, a larger fraction of the ADC is unaccounted for by 168 h in NHP plasma (~ 75%) compared to mouse plasma (~ 25%). This is consistent with the in vivo observation of higher inactive ADC overestimation in mice compared to NHP, where inactive ADC/conjugated IP-1 is calculated as [total mAb - active ADC] to maintain mass balance. Overall, since the model calibration captures the active ADC/conjugated active payload (molecular species driving efficacy) profile well in both animal species, and the overestimation of the inactive ADC is likely due to untracked ADC species (all likely inactive), the model calibration was deemed fit for purpose.

<!-- PARAGRAPH_START [direct] -->Additionally, the in vitro inactivation half-life (calculated as loge2/ki.a.) in mouse plasma (39–42 h, Figure S3) was consistent with that estimated from the in vivo PK model (~ 52 h), but the NHP values diverged. In vitro NHP plasma stability indicated an inactivation half-life of ~ 17 h, while fitting from in vivo PK model estimated a half-life of ~ 61 h. This difference was not due to parameter identifiability issues – indeed forcing the inactivation half-life to a constant value of 17 h did not yield good fits.

### PK-TGI modeling of mouse tumors indicates both Cmax and exposure driven efficacy

<!-- PARAGRAPH_START [direct] -->The Gompertz growth model described the vehicle-treated tumor growth kinetics well. Keeping the population mean and variance estimates of the tumor growth rate (β) and PK parameters constant (but linked to individual animal IDs for the latter), the impact of four DYP688 treatment groups from a single study was evaluated using a linear killing, one-transit compartment TGI model. Parameters from the model are listed in Table 3. Tumor responses within each treatment group of the 92.1 CDX tumor model was heterogeneous, though the individual data (Figure S4) and overall trends in the data were captured well by the median and 90% prediction quantiles (Fig. 3A). Interestingly, the estimates of tumor killing rate constant (kkill) in individual mice showed a skewed distribution and co-varied with dose (Figure S4). More complex models, including those with different reaction schemes for non-linear killing effects, did not further improve the predictability of the model, certainty of estimates, or eliminate the dose covariate on killing rate constant. We additionally tested a tumor growth modulation model where tumor growth rate influences tumor killing (Figure S5). While this model did reduce the dose covariate on the killing rate constant, the predictions considerably worsened. Since the goal was to capture the observed tumor kinetics in all dose groups, the initial TGI model with the dose-covariate was retained and used for further simulations. Though not directly confirmed, it is likely the kkill-dose covariance arises from the physiological response of 92.1 cells to FR, wherein treatment with increasing amounts of FR resulted in re-differentiation of 92.1 cells, adopting a “flat” morphology, growing slower than the parent cells, and demonstrated increased melanosome content [18]. This is consistent with the observations of “flat” tumors (tumors with flattened, disc like morphology with immeasurable height) with much slower regrowth was prominent in 3 mg/kg and 6 mg/kg groups with increasing prevalence. Note, the ‘flat’ morphology is not universally observed in all melanomas. Indeed, published accounts of melanoma cell lines treated with FR indicate that the dark/flat morphology is more commonly observed with Class 1 melanoma cell lines (which includes 92.1), while Class 2 melanomas which have a higher metastatic potential, do not necessarily exhibit this morphology upon treatment with FR [18–21]. In-house evaluation of DYP688 with a PDX tumor model did not exhibit the appearance of the flat morphology even at 5x higher doses than in the 92.1 model (data not shown), indicating this morphological change may not be universally applicable to patients, particularly those with metastases. Since the volume calculation for such tumors was difficult, they were censored as below limit of quantification (‘BLOQ’) in the dataset during model fitting. Additionally, the fitted PK (Figure S6, Supplementary Table 2) of a mutant version of DYP688 that does not bind to FcRn receptors and thus exhibits rapid clearance (‘FcRn-mut’) was performed. Validation of the PK-TGI model developed using 92.1 CDX tumor data was performed by overlaying the predicted PK and/or efficacy profiles of DYP688 (Figure S7A) and its ‘FcRn-mut’ variant (Figure S7B) to observed data from an independent study not employed for the calibration. Predictions agreed qualitatively well with the independent datasets, highlighting reliability of PK-TGI model for further exploratory analysis.

<!-- PARAGRAPH_START [direct] -->Table 392.1 CDX tumor growth inhibition model parameters inferred from mice

| Parameter | Notation (unit) | Estimate | RSE (%) | IISD | IISD RSE (%) |
| --- | --- | --- | --- | --- | --- |
| 92.1 tumor growth parameters estimated from untreated tumor data | | | | | |
| Tumor growth rate | β (1/h) | 0.0059 | 17.2 | 0.46 | 30.4 |
| Maximum tumor volume | TSmax (mm3) | 928.39 | 8.48 | 0.26 | 22.6 |
| Additive log normal measurement error | a (mm3) | 0.15 | 7.7 | - | - |
| TGI model parameters estimated from DYP688-treated groups in 92.1 CDX tumors | | | | | |
| Tumor killing constant | kkill (1/nM) | 0.00016 | 47.4 | 0.9 | 19.4 |
| Dose-dependent kkill covariate (for 3 mg/kg group) | beta\_kkill\_3 | 0.72 | 75.3 | - | - |
| Dose-dependent kkill covariate (for 6 mg/kg group) | beta\_kkill\_6 | 1.18 | 51.7 | - | - |
| Transit compartment time | τ (h) | 698.87 | 0.0182 | 0.1 | - |
| Additive log-normal measurement error | a (mm3) | 0.73 | 3.35 | - | - |

<!-- PARAGRAPH_START [direct] -->Fig. 3(A) Population PK-TGI fitting of tumor growth inhibition data in 92.1 CDX tumors, (B) PK-TGI correlation analysis identifying exposure above tumorostatic concentration (TSC) as the PK-driver of efficacy

<!-- PARAGRAPH_START [direct] -->Using the validated PK-TGI model developed using 92.1 CDX data, dose fractionation simulations were performed and different PK metrics, namely peak concentration (Cmax), exposure (AUC), and time over tumorostatic concentration, were correlated to the predicted tumor growth inhibition (Fig. 3B). Tumorostatic concentration (TSC) is defined here as the conjugated active payload concentration at which achieves tumor stasis and is calculated according to the equation below. Note, since PK-TGI model based on the 92.1 tumor model (CDX) had dose as a covariate on kkill, we selected the smallest kkill i.e., largest TSC (Table 4) as a conservative estimate for subsequent analysis.

<!-- PARAGRAPH_START [direct] -->Table 4Killing rate constant and tumorostatic concentration (TSC) inferred from 92.1 CDX TGI model

| Dose | Median killing rate constant (1/nM) | TSC (nM) |
| --- | --- | --- |
| 1.5 mg/kg | 0.00016 | 127 |
| 3 mg/kg | 0.00026 | 34 |
| 6 mg/kg | 0.00058 | 13.2 |

\documentclass[12pt]{minimal}
\usepackage{amsmath}
\usepackage{wasysym}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{amsbsy}
\usepackage{mathrsfs}
\usepackage{upgreek}
\setlength{\oddsidemargin}{-69pt}
\begin{document}$$\:{TSC}=\:\frac{\beta\:\*log\frac{{TS}\_{max}}{{TS}\_{0}}}{{k}\_{kill}}$$\end{document}

<!-- PARAGRAPH_START [direct] -->All three metrics only showed limited/incomplete correlation to TGI response. For example, Cmax showed reasonable correlation with response with DYP688; however, a confirmatory assessment with equivalent doses of ‘FcRn-mut’, the Fc-mutant variant (i.e., same Cmax, different AUC) breaks the correlation, indicating that Cmax alone does not predict response. However, further assessment of AUC showed that AUC-matched groups with lower Cmax showed less response (e.g., 3 mg/kg Q2W vs. 6 mg/kg Q4W). Time over TSC showed a similar incomplete correlation pattern. To account for both Cmax, AUC, and time over TSC dependent response, we calculated a composite metric denoted exposure above TSC (AUCTSC). AUCTSC captures the improved response from 1.5 to 3 to 6 mg/kg, as the peak concentration progressively surpasses the TSC, while the concentration stays above the TSC longer with higher Cmax or more frequent dosing, thereby improving response. Therefore, our analysis indicated that AUCTSC is a good PK surrogate for TGI response, establishing it as a reliable metric for human dose translation and optimization. Additional mechanistic exploration of the AUCTSC-driven response and related impact of DYP688 tumor distribution, antigen binding, intracellular payload accumulation/binding on anti-cancer activity is discussed in the following section.

### Mechanistic modeling highlights AUC

<!-- PARAGRAPH_START [direct] -->Tumor response driven partially by the peak ADC concentration may appear atypical for ADCs, as most are assumed to exhibit cumulative exposure-driven response. Cmax-driven response has previously been hypothesized as an outcome when the tumor penetration of the ADC is heterogeneous i.e., tumor cells in the furthest edge of the tumor do not encounter any ADC due to the binding site barrier effect [22]. To assess if tumor penetration depth limitations explain the partial Cmax driven response of DYP688, we performed mechanistic Krogh cylinder simulations (Fig. 4A) based on a previously validated model [15]. The Krogh cylinder model simulations are fully predictive and not fit to any data. Parameters for the model are listed in Supplementary Table 1. In particular, PMEL17 expression was determined to be moderate (~ 100,000 receptors/cell), based on in-house immunohistochemistry (IHC) scoring of ~ 1.5–2 (data not shown, [23]). Immunohistochemistry detects both surface and intracellular PMEL17, and literature evidence that suggests that up to 45% of the total cellular PMEL17 can be present on the cell surface [24]. Assessment of conjugated active payload (ADC species driving efficacy) tumor penetration for 100,000 receptors per cell shows that at the peak concentration, some active ADC reaches the edge of the tumor, even at 1.5 mg/kg (Figure S8A). This indicates that the Cmax-driven response with DYP688 is likely not due to the canonical hypothesis that increasing Cmax improves tumor penetration of the ADC, and therefore efficacy. Even, at the extreme limit of IHC 2 expression of 500,000 receptors per cell, active ADC at 1.5 mg/kg is predicted to target cells at the edge of the tumor (R = 100 μm). This suggests that Cmax-driven improvement in efficacy at higher dose levels of DYP688 is likely not related to better tumor penetration.

<!-- PARAGRAPH_START [direct] -->Fig. 4(A) Graphical schematic of Krogh cylinder model incorporating tumor uptake of three ADC species deriving from plasma inactivation of DYP688 (2 active payloads, one active payload, and no active payloads), competitive binding of the ADC species to surface receptor (PMEL17), and internalization of the receptor-ADC complex. Payload inactivation was assumed to not occur in the tumor microenvironment, based on experimental observations. In the lysosome, the active payload is released either from linker cleavage or antibody backbone degradation, and the amount of active payload release is dependent on the corresponding DAR of each ADC species. For simplicity, the presence of any inactive payload was considered functionally equivalent to no active payload (e.g. unconjugated antibody formed from release of both active payloads was considered equivalent to an ADC with two inactive payloads and pooled in the same state variable). The released active payload permeates from the lysosome to the cytoplasmic space, where it could either reversibly bind Gαq/11 or escape the cell, diffuse into adjacent cells, and mediate bystander Gαq/11 inhibition. (B) Overlay of predicted fraction of free Gαq/11 (lines) with the observed pERK score (symbols) showed the model captures the kinetics of DYP688 anti-cancer activity well. (C) Predicted fraction Gαq/11 free at peak inhibition showed good correlation between time-independent tumor growth inhibition at different doses over a 14-day period (single administration). Time-independent tumor growth inhibition is presented as area-under-curve of tumor volume ratio (Figure S9), normalized to the area-under-curve of untreated group

<!-- PARAGRAPH_START [direct] -->We further assessed the mechanism behind the AUCTSC-driven response by simulating the payload activity in the cell. Payload release into the lysosome was modeled as linker cleavage based on the known stability of the valine-citrulline linker [25] and payload escape, bystander effects, and intracellular Gαq/11 binding was adapted based on the previously established Krogh cylinder model for cytotoxic agents [15]. In addition to using FR and Gαq/11 specific parameters in the model (Supplementary Table 1), Gαq/11 target turnover was also included in the model. The predicted radial average and standard deviation of available fraction of intracellular Gαq/11 (“fraction Gαq/11 free”) was compared to the pERK score for three dose levels – 1.5 mg/kg, 6 mg/kg, and 12.5 mg/kg DYP688 (Fig. 4B). A dose of 1.5 mg/kg delivers an intracellular FR concentration sufficient inhibition only ~ 50% Gαq/11, consistent with only partial pERK downregulation. At 12.5 mg/kg, pERK downregulation was maximized and more durable, consistent with the predicted Gαq/11 inhibition. The peak Gαq/11 inhibition at 6 mg/kg was also consistent with maximum pERK downregulation, though the rebound of Gαq/11 levels appears to occur faster than pERK rebound. Overall, the trends in predicted Gαq/11 inhibition corresponds with the observed trend in pERK scores, and 90% Gαq/11 inhibition (i.e., 10% free Gαq/11) was approximated as the threshold at which growth signaling rebounds. Furthermore, we compared the predicted peak fraction Gαq/11 free to the observed tumor growth inhibition for different doses of DYP688 and a high dose of the FcRn-mut variant (Fig. 4C). A strong correlation was observed, with > 90% Gαq/11 inhibition correlating with tumor stasis and > 99% Gαq/11 inhibition correlating to durable tumor regression. These observations are consistent with the hypothesis that increasing Cmax is driving better tumor saturation rather than tumor penetration [22]. The detailed spatial profile of Gαq/11 inhibition (Figure S8A) highlights that the intra-dose radial range of peak Gαq/11 inhibition is relatively uniform at 100,000 PMEL17/cell (i.e. no tumor penetration limitations), but undersaturated at 1.5 mg/kg (40–60%) and increases proportionately with dose. Correlations between AUCTSC for a single administration and peak receptor occupancy (i.e., proxy for tumor penetration, Figure S8B) or average Gαq/11 inhibition over a 7-day period (i.e., proxy for tumor saturation, Figure S8C) further highlights a poor correlation to tumor penetration and better correlation to tumor saturation. Detailed PK, Gαq/11, and tumor growth inhibition profiles for different dosing schemes are shown in Figure S9.

<!-- PARAGRAPH_START [direct] -->Overall, these results provide a mechanistic explanation as to why both Cmax and AUC correlate to tumor response. Achieving a Cmax above the TSC is critical for ensuring the minimum threshold of Gαq/11 inhibition which drives downstream tumor cell growth inhibition (indicated by pERK). Progressively higher doses serve to (1) achieve maximum inhibition of the target, leading to no downstream growth signaling, and (2) prolongs > 90% inhibition of Gαq/11, driving drastically reduced downstream signaling for longer durations. Furthermore, at doses that exceed the TSC, more frequent dosing allows more sustained inhibition of Gαq/11, and therefore downstream growth signaling. Thus, the AUCTSC-driven response for DYP688 is attributed to the ‘targeted’ mechanism of action of the FR payload, which is different from the tumor payload-exposure mechanism of action of most non-specific cytotoxins [26] utilized on all currently approved ADCs.

### DYP688 elimination and inactivation rates are well-matched to optimize payload activity

<!-- PARAGRAPH_START [direct] -->Simulations presented in the previous sections specifically focused on analyzing the behavior of DYP688, accompanied by a mechanistic understanding of its pharmacology. In this section, we use the above validated DYP688 Krogh cylinder model to extrapolate generalized learnings to aid in the design of targeted-targeted ADCs that exhibit linker/payload metabolism in circulation. Note, the following analysis is not limited just to payload inactivation – it is also applicable to common ADC pharmacology such as payload deconjugation.

<!-- PARAGRAPH_START [direct] -->One of the key findings of the semi-mechanistic systemic PK-TGI modeling of DYP688 showed that the payload inactivation rate (ki.a.) and the antibody elimination rate (kel) are approximately the same in both mice and NHPs. However, it is not necessary that these two rates are always matched to enable sufficient exposure of active payload. We therefore used the PK-TGI model to explore the impact of designing ADCs with varying biotransformation (payload inactivation, deconjugation, etc.) half-life and elimination half-life. A heat map of the impact of biotransformation versus elimination on the predicted preclinically efficacious exposure (AUCTSC) is shown in Fig. 5A. Compounds that lie further to the right of the plot represent progressively more stable ADCs, thus a longer half-life of the ADC (moving up the plot) progressively approaches the maximum effective ADC exposure at any given dose. Compounds that lie further left of the plot represent progressively decreasing stability, so slowing the clearance half-life (moving up the plot) does not significantly change preclinically efficacious exposure of the conjugated active payload. The elimination half-life of DYP688 is near-ideally matched to the inactivation half-life, such that the ADC species begin to clear significantly just as they start to inactivate (i.e., do not contribute to efficacy). For a hypothetical DYP688 with much faster elimination (hDYP688-fast, blue star), the conjugated active payload is prematurely eliminated, and would require either a higher dose or more frequent dosing to achieve preclinically efficacious exposure. On the other hand, a hypothetical DYP688 with much slower elimination (hDYP688-slow, red star) does not significantly increase predicted preclinically efficacious exposure, as most of the payload has already inactivated. In this scenario inactive ADC would be circulating unnecessarily, creating greater risk for undesired side effects such as anti-drug-antibody (ADA) development. Therefore, DYP688 lies on the sweet-spot of the heat map where there is just enough exposure to achieve sufficient anti-cancer activity at low-to-moderate doses.

<!-- PARAGRAPH_START [direct] -->Fig. 5(A) Heatmap of predicted preclinical ADC efficacious exposure as a function of antibody backbone elimination half-life and payload inactivation half-life. Efficacious exposure is reported as fold-factor of exposure above threshold (AUCTSC) estimated for DYP688. (B) DYP688 (black circle) and two hypothetical ADCs exhibiting same payload inactivation half-life as DYP688, but faster antibody elimination (hDYP688-fast, blue star) or slower antibody elimination (hDYP688-slow, red star) were simulated using the Krogh cylinder tumor model. The stoichiometric ratio of active ADC to inactive ADC at steady state active ADC Cmax, surface PMEL17 occupancy at active ADC Cmax, and fraction Gαq/11 available at peak inhibition were predicted for each ADC design

<!-- PARAGRAPH_START [direct] -->The above exploratory analysis was based purely on plasma exposure. In this section, we compare the plasma pharmacokinetics, tumor PMEL17 occupancy, and predicted free fraction Gαq/11 (corresponding to efficacy, Fig. 4C) of DYP688 (tel ~ ti.a. ) and the two hypothetical compounds shown on the heatmap – DYP688 but with faster antibody elimination (‘hDYP688-fast’, tel < ti.a.) and DYP688 but with slower elimination half-life (‘hDYP688-slow’, tel > ti.a.). First, if we assume that no inactivation occurs, the total antibody (tmAb, dashed line) curve represents the hypothetical maximum ADC exposure, and corresponding tumor receptor occupancy and Gαq/11 activity (Figure S10). As expected, slower elimination yields more sustained tumor receptor occupancy and Gαq/11 inhibition. When the effect of inactivation is added in (same for all three compounds), an anticipated decrease in plasma ADC concentration, less tumor receptor occupancy, and less Gαq/11 inhibition (solid line) is predicted at the first dose compared to the no inactivation scenario.

<!-- PARAGRAPH_START [direct] -->For hDYP688-fast, elimination occurs faster than inactivation, leading to no accumulation of the inactive ADC species. Thus, the steady state profiles look identical to the first dose profile (Figure S10). In this scenario, the steady state plasma stoichiometry of active ADC to inactive ADC is extremely high at every new dose administration (containing 99% active ADC), consistently driving maximum binding of the active ADC to PMEL17 and maximum inhibition of Gαq/11 in tumors (Fig. 5B). For hDYP688-slow elimination occurs slower than inactivation, leading to significant accumulation of the inactive ADC species at steady state (Figure S10), to the extent that steady state plasma stoichiometry of active ADC to inactive ADC reduces to 1 at every new dose administration. This creates significant PMEL17 binding competition for the active ADC in the tumor, which in turn reduces payload accumulation in the cell, driving less Gαq/11 inhibition at steady state compared to the first dose (Fig. 5B). The design of DYP688 that near-perfectly matches the antibody elimination half-life to the inactivation half-life leads to minimal accumulation of the inactive ADC at steady state (Figure S10). While this results in some reduction in the peak plasma stoichiometry, it is still several-fold larger than hDYP688-slow. Thus, there is minimal competition for PMEL17 binding to the active ADC, and peak Gαq/11 inhibition remains strong and durable compared to the first dose (Fig. 5B). Thus, the design of DYP688 is ideally optimized for payload activity.

### Predicted human PK parameters consistent with observed clinical PK

<!-- PARAGRAPH_START [direct] -->Human PK parameters were extrapolated by single species weight-based allometric scaling from cynomolgus monkey PK parameters and are listed in Table 5. Volume (V) and clearance (CL) scaling was based on the findings of Li et al. that showed from a retrospective analysis of 11 ADCs that an exponent of 1 when scaling from cynomolgus PK data alone can sufficiently predict human PK for ADCs exhibiting linear PK [12]. For the inactivation rate (ki.a.), the in vivo estimate of ~ 61 h was assumed the more likely value for parameter scaling. Based on the similar inactivation rates between NHP and human in vitro (Fig. 2C), human inactivation rate was considered the same as NHP inactivation rate. The predicted human PK closely matches the observed interim clinical PK (cut-off date 8 Sept 2023) [27], as demonstrated by the cycle 1 comparison for the 8 mg/kg dose group (Fig. 6), highlighting that the reliability of the preclinical to clinical translation employed, including the non-conventional payload inactivation kinetics.

<!-- PARAGRAPH_START [direct] -->Table 5Allometric scaling of PK parameters from NHP to human

| Parameter | Estimated Cynomolgus PKMedian (IQR) | Predicted Human PKMedian (IQR) | Comments |
| --- | --- | --- | --- |
| Plasma volume, Vc (L) | 0.083  (0.075–0.095) | 1.93  (1.75–2.21) | \documentclass[12pt]{minimal} \usepackage{amsmath} \usepackage{wasysym} \usepackage{amsfonts} \usepackage{amssymb} \usepackage{amsbsy} \usepackage{mathrsfs} \usepackage{upgreek} \setlength{\oddsidemargin}{-69pt} \begin{document}$$\:{V}\_{human}={V}\_{cyno}{\left(\frac{{BW}\_{human}}{{BW}\_{cyno}}\right)}^{w}$$\end{document}  w = 1a |
| Linear elimination, CL (L/hour) | 0.00108  (0.00098–0.00124) | 0.0251  (0.0228–0.0287) | \documentclass[12pt]{minimal} \usepackage{amsmath} \usepackage{wasysym} \usepackage{amsfonts} \usepackage{amssymb} \usepackage{amsbsy} \usepackage{mathrsfs} \usepackage{upgreek} \setlength{\oddsidemargin}{-69pt} \begin{document}$$\:{CL}\_{human}={CL}\_{cyno}{\left(\frac{{BW}\_{human}}{{BW}\_{cyno}}\right)}^{w}$$\end{document}  \documentclass[12pt]{minimal} \usepackage{amsmath} \usepackage{wasysym} \usepackage{amsfonts} \usepackage{amssymb} \usepackage{amsbsy} \usepackage{mathrsfs} \usepackage{upgreek} \setlength{\oddsidemargin}{-69pt} \begin{document}$$\:CL={V}\_{c}\times\:\:{k}\_{el}$$\end{document}  w = 1a |
| Plasma to peripheral distribution, k12 (1/hour) | 0.064  (n/a) | 0.064  (n/a) | Assumed same in cyno and human |
| Peripheral to plasma distribution, k21 (1/hour) | 0.039  (0.03–0.046) | 0.039  (0.03–0.046) | Assumed same in cyno and human |
| Initial fraction of inactive ADC, fr | 1% | 1% | Assumed same in cyno and human |
| Payload ring-opening inactivation, t1/2,inactivation (hours) | 61  (59–63) | 61  (59–63) | Assumed same in cyno and human based on in vitro stability data |

aLi et al., Clin. Transl. Sci., 2019

<!-- PARAGRAPH_START [direct] -->Fig. 6Interim clinical PK data (cut-off date 8 Sept 2023, 12 patients) for total antibody (black) and conjugated active payload (blue) overlayed with anticipated human PK scaled allometrically from NHP demonstrates reliability of the scaling method applied, particularly for the payload inactivation. Anticipated PK simulations performed assuming body weight of 70 kg and a 1.5-hour infusion duration, based on the median interim clinical infusion time for the displayed cohort

## Discussion

<!-- PARAGRAPH_START [direct] -->Antibody-drug conjugates are complex molecules consisting of several design components that can greatly influence the pharmacological activity of the drug. There has been a significant expansion in the roster of clinically approved ADCs, yet several more continue to fail due to lack of response or limited tolerability, and very few “one-size-fits-all” design features have been definitively linked to clinical success or failure. One of the missing links to this design-developability chasm is assessments of the pharmacokinetics (PK) driver of activity early in the candidate selection stage. Drug efficacy and safety are both driven by its systemic and local PK, and in turn, the PK is directly influenced by the different ADC components. Furthermore, an ADC is more than the sum of its parts, yet ADC components selection often occurs independent of the other components, or even other features of the same component, missing potential interdependencies that could drive altered pharmacology. A classic example is conjugating a relatively high drug-to-antibody ratio (DAR) of a highly potent payload to an antibody with strong binding affinity against a highly expressed antigen (e.g. trastuzumab emtansine/T-DM1). The DAR 4 results in a tumor sub-saturating maximum tolerated dose (MTD), while the combination of a strong affinity and high antigen expression limits tumor distribution of the ADC due to the “binding site-barrier” effect [28]. As a result, perivascular cells receive the bulk of the cytotoxin resulting in cellular ‘overkill’, and no ADC is delivered to the rest of the tumor cells [29]. In this scenario, simply choosing a lower DAR instead (achieved by diluting the ADC with unconjugated antibody) allows increasing the total antibody dose without changing the total payload dose, enabling uniform delivery of the highly potent payload throughout the tumor, driving better efficacy preclinically [30].

<!-- PARAGRAPH_START [direct] -->Historically, the efficacy of ADCs was assumed to be driven by the ADC plasma exposure, calculated as the area-under-the-curve (AUC) of the ADC PK profile. While such an assumption is convenient, the kinetics of ADC delivery in tissue/tumors is known to be considerably more complex than represented by the ADC plasma profile. Indeed, a retrospective analysis by Zhang and colleagues [26] shows that in dose fractionation PK-efficacy studies with PBD, MMAE, and maytansine ADCs, cumulative plasma exposure did not correlate with observed preclinical efficacy, but cumulative payload concentration in the tumors did. A similar study by Jumbe et al. shows that lower, but more frequent dosing of T-DM1 (maintaining the same total payload dose), resulted in poorer preclinical efficacy [31]. On the other hand, Hinrichs et al. have shown that dose fractionation administration of a PBD-based ADC did not greatly impact preclinical efficacy [32]; however only two doses-match groups were tested (1.5 mg/kg & 0.5 mg/kg x 3; 1 mg/kg & 0.33 mg/kg x 3), all of which are considerably high doses relative to PBD potency and could potentially be outside the dynamic dose-response range. Overall, these analyses highlight that ADCs do not always show cumulative plasma exposure-driven response, and dedicated analysis of well-designed studies (such as dose-fractionation treatments) are necessary to identify the correct PK-driver of efficacy. This is even more critical for the newer generation of ADCs that favor selectively targeted payloads that often function by disrupting a complex network of signaling pathways.

<!-- PARAGRAPH_START [direct] -->In this work, we performed similar plasma PK-TGI correlation analysis as Zhang et al. [26] of two simple, but well-designed tumor growth studies. In the first study, total payload-matched treatment groups were devised to assess if response is exposure-driven (Fig. 3A). In the second study, Cmax-matched treatment groups were devised using an FcRn-mutant variant of DYP688, which matched DYP688 in peak concentration, but showed rapid clearance due to the lack of FcRn-mediated endosomal recycling (Figure S6). Interestingly, our analysis showed a unique outcome where both Cmax and exposure are partially responsible for driving preclinical efficacy. To capture both these metrics into a single, clinically assessable metric, we derived a composite active ADC exposure-above-tumorostatic concentration (AUCTSC) metric that correlates best with efficacy across both preclinical studies. By calculating an exposure above a threshold > 0 (i.e., TSC), we capture the lack of response in low dose groups (e.g. 1.5 mg/kg) which have a plasma PK profile below the TSC. On the other hand, increasing dose corresponds to an increasing Cmax, and given the relatively rapid clearance of the active ADC (elimination and inactivation), an increasing Cmax non-linearly increases AUCTSC, thereby capturing the Cmax-driven effect too.

<!-- PARAGRAPH_START [direct] -->To understand mechanistically why the preclinical efficacy of DYP688 is both Cmax and cumulative exposure driven, we performed QSP modeling of DYP688 tumor disposition and FR-mediated inhibition of Gαq/11. Our mechanistic simulations showed that the Cmax-driven response may arise from the mechanism of payload action. Unlike microtubules and DNA, pathway proteins often need to achieve near-complete inhibition by drugs to significantly disrupt intracellular growth signals. Here, higher Cmax predicts increasing inhibition of Gαq/11, but significant impact on tumor growth is not observed until 90–99% Gαq/11 inhibition is achieved (Fig. 4C). Indeed, Gαq/11 knockout studies in embryonic mice suggest a > single knockout of either gene (equivalent to > 50% knockdown) considerably increased lethality [33]. This is consistent with the empirical analysis that indicates a minimum Cmax (i.e., TSC) is required to deliver enough payload to the tumor to sufficiently perturb the growth signaling pathway. A single dose of 3 mg/kg just achieves enough Gαq/11 inhibition for tumor stasis but fractionating the dose to 1.5 mg/kg x 2 does not result in similar cumulative Gαq/11 inhibition. On the other hand, the longer the plasma concentration stays above TSC (i.e. higher plasma exposure), the greater the durability of Gαq/11 inhibition. Thus, a 12.5 mg/kg dose of FcRn-mut that is cleared within hours of administration does not deliver sufficient FR to the tumor to disrupt growth signaling, but the same dose of DYP688 that clears over the course of a few days sustainably delivers FR to the tumor to achieve durable growth signaling disruption (Figure S9). Ultimately, the QSP analysis shows that durable delivery of FR in tumors is what directly drives response; however, QSP modeling is not always a feasible option for clinical analysis. Thus, performing empirical assessments that robustly identify the most relevant PK-driver of efficacy based on clinically relevant datasets (i.e. plasma PK) can more effectively guide clinical development.

<!-- PARAGRAPH_START [direct] -->For the T-DM1 example described earlier, the presence of an “inert” unconjugated antibody is beneficial to the ADC pharmacology; however, this is not always the case. Here, we highlight how the antibody backbone design needs to be refined in the context of the linker/payload kinetics to limit unnecessary systemic accumulation of inactive ADC species (functionally equivalent to unconjugated antibody from an efficacy standpoint) that can lead to surface receptor binding competition for low-to-moderately expressed antigens for every dose administration at steady state (Figure S10). Additionally, unlike the antibody-coadministration described above, here the inactive ADC species is sequentially formed from the active ADC species. Consequently, the accumulation of inactive ADC species does not benefit the pharmacology of the active ADC (such as blocking receptors on normal tissue, etc.). The prolonged persistence of inactive ADC, driven by the longer half-life of the antibody backbone (a canonical ADC design dogma), instead serves as a potential liability for the therapeutic index (competitive binding to tumor receptors, increased risk of ADA formation, etc.). In this context, DYP688 possesses ideally matched design features where the unconventionally short antibody elimination half-life matches the rate for formation of the inactive ADC species, such that these inactive ADC species are consistently cleared as they are formed.

<!-- PARAGRAPH_START [direct] -->In the context of payload metabolism, often one or more inactive species may form, and developing assays to track all inactive species can be challenging. Here, the LBA and PRA assays were developed against one specific inactive payload species (denoted here as ‘IP-1’). However, as seen in Fig. 2C, the sum of FR conjugated ADC (“active ADC”) and IP-1 conjugated ADC (“inactive ADC”) is only ~ 25% of the initial total ADC concentration at 168 h. Since there is no elimination and only biotransformation in vitro, this defies the laws of mass balance conservation - unless the payloads are undergoing further metabolism to other species not detected by the IP-1-specific LBA and PRA. Indeed, the deviation of mass balance conservation only occurs once most of the active FR species have been converted to IP-1 species (Fig. 2C). The PK model, on the other hand, enforces mass balance conservation i.e. inactive ADC = Total antibody – Active ADC. Thus, the inactive ADC PK predicted with the model captures all inactive ADC species, not just those detected by the assay. This is the source of “misspecification” in observed vs. predicted PK profile in NHP (Fig. 2B), and indeed in human (data not shown). However, we would offer that this prediction “misspecification” instead presents a clinically relevant perspective not provided by the data alone. In this case, all species carrying an inactive payload, or even no payload (DAR0), compete with the active ADC for tumor surface receptors, not just the select ones that are measured (e.g. IP-1). Thus, quantitative modeling that enforces the laws of mass balance conservation offers a rapid solution to assess the impact of all competitive inactive ADC species.

<!-- PARAGRAPH_START [direct] -->Importantly, while payload inactivation may seem like a niche feature not commonly observed with ADCs, it is functionally equivalent from an efficacy standpoint to other common linker-payload features such as payload deconjugation. Payload inactivation results in the sequential formation of ADC carrying pharmacologically inactive payloads, while payload deconjugation results in a ‘DAR0 ADC’ species carrying no active payloads. Functionally, both ADC species maintain the same antibody kinetics, but do not contribute to payload-driven efficacy (nor does the inactive payload directly impede the active payload). Therefore, a hypothetical ADC with slower antibody elimination half-life compared to its payload deconjugation half-life is likely to show similar tumor pharmacology as predicted for hDYP688-slow due to competition from unconjugated antibody. This is particularly fascinating for the scope of ADC bioanalysis guidelines – tracking the total antibody species, various DAR > 0 species, and the released payload is common practice in the field of ADCs. In the case of DYP688, tracking ADCs with an inactive metabolite of payload is required for assessing several TI implications. The implications towards efficacy from competitive receptor occupancy (RO) presented here may seem obvious in hindsight, yet that tracking a functionally equivalent DAR0 antibody species formed from payload deconjugation is rarely a consideration despite similar implications towards competitive RO.

<!-- PARAGRAPH_START [direct] -->Notably, there are additional nuances that determine when such competitive RO may be detrimental to ADC efficacy. One such consideration is the mechanism of action and potency of the payload. DYP688 carries a reversibly binding protein inhibitor FR that targets a selective protein in the cancer growth signaling pathway. (Note: FR has previously been described as a “pseudo-irreversible” [8]; however, this may be relative to the residence time of other Gαq/11 binders and/or the elimination half-life of the small molecule alone. In the context of the ADC, the dissociation rate/residence time is on the timescales of other ADC cellular kinetics, and therefore described as reversible here). FR (and “targeted” payloads in general) are distinct from pan-cytotoxic payloads that currently dominate the clinical landscape. From a fundamental mechanism of action, pan-cytotoxic agents only need to accumulate to certain intracellular thresholds [7, 34] before irreversibly destabilizing the cell, and delivering increasingly more payload beyond this threshold does not improve response (‘overkill hypothesis’) [26]. On the other hand, targeted payloads reduce the functionality of selective proteins (via inhibition, degradation, etc.) to disrupt growth signaling pathways, and durable disruption of the pathway can trigger apoptosis. Unlike pan-cytotoxins that can bind pseudo-irreversibly and do not need to completely inhibit the target, targeted payload binding is more sensitive to the interplay of ADC kinetics. Using the example of DYP688 and the hypothetical hDYP688-slow kinetics, we see that while payload delivery is predicted to be blunted at steady state with the latter (Figure S11), it is still higher than the therapeutic threshold for most cytotoxic payloads (~ 150nM for microtubule inhibitors [6, 15]), likely similar for topoisomerase inhibitors and lower for DNA interacting payloads [34]. This suggests that an ADC carrying a typical pan-cytotoxic payload would still show reasonable anti-cancer activity, minimizing the impact of competitive RO. However, in the case of FR which binds more reversibly to Gαq/11, the blunting of payload delivery to the cell is sufficient to impede durable Gαq/11 inhibition at steady state, impacting response. For DYP688, the sustained regression of 92.1 CDX tumors is partly driven from the high sensitivity of this cell line to FR [19]. Patient tumors may not be as sensitive, as seen with the differential response of patient-derived xenograft (PDX) tumors to FR compared to 92.1 [20].

<!-- PARAGRAPH_START [direct] -->Among the FDA-approved ADCs, sacituzumab govitecan (SG) defies several canonical ADC design dogmas (moderate binding affinity to TROP-2, unstable linker, relatively low potency & inactivating payload, etc.), yet it may be this exact combination of “fail” design features that could be driving favorable pharmacokinetics contributing to its success – (1) the short linker stability results in SN38 falling off the ADC (and potential free SN38 inactivation in the plasma at neutral pH [35]) likely prevents significant TROP-2 mediated payload accumulation in normal tissue compared to other TROP-2 ADCs that failed [36, 37] while also allowing more efficient payload release in tumors [37, 38]; (2) the shorter half-life of the antibody backbone prevents unnecessary circulation of pharmacologically inactive unconjugated antibody, enabling weekly dosing (76115Orig1s000.pdf); (3) the lower binding affinity to TROP-2 (76115Orig1s000.pdf) and relatively lower potency of SN38 [39] enables 2x higher DAR and 2-3x higher doses than most other approved solid-tumor ADCs, reducing the potential for tumor distribution heterogeneity. While DYP688 is significantly different from SG (and indeed from most clinical ADCs), the two ADCs highlight the value in utilizing canonically unconventional ADC design features that drives optimized pharmacology (and thus response), rather than a cookie-cutter design approach.

## Conclusions

<!-- PARAGRAPH_START [direct] -->In conclusion, approaching ADC development with an updated “design-pharmacology-response” perspective, aided by identification of PK-drivers of efficacy and quantitative systems pharmacology, can evolve the existing design dogmas towards supporting better discovery-to-clinic success, particularly for “targeted-targeted” ADCs.

## Electronic supplementary material

<!-- PARAGRAPH_START [direct] -->Below is the link to the electronic supplementary material.

<!-- PARAGRAPH_START [direct] -->Supplementary Material 1

## Other Content
<!-- PARAGRAPH_START [outside] -->Publisher’s note

<!-- PARAGRAPH_START [outside] -->Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.

<!-- PARAGRAPH_START [outside] -->Eshita Khera and Lekshmi Dharmarajan contributed equally to this work.

<!-- PARAGRAPH_START [outside] -->We thank Victor Chang for early modeling efforts that supported this work. We thank the significant contributions of the project team in Novartis Biomedical Research Oncology, Novartis Biologics Research Center, and Novartis Translational Medicine. We additionally thank the ADC community at Novartis Biomedical Research and colleagues in the PKS Modeling & Simulation group for critical feedback.

<!-- PARAGRAPH_START [outside] -->Conceptualization: EK, LD, JDK, DH. Methodology: EK, LD. Formal analysis and investigation: EK, LD. Experimental data collection: VE, HV, JD, NE, VR, KW. Data curation: DH, SS, EK, LD. Visualization: EK, LD. Writing—original draft preparation: EK. Writing—review and editing: EK, LD, JDK, DH, VE, HV, JD, NE, VR, SS. Supervision: JDK.

<!-- PARAGRAPH_START [outside] -->No datasets were generated or analysed during the current study.

<!-- PARAGRAPH_START [outside] -->All authors were employees of Novartis at the time the work was performed. EK, JDK, LD, VE, HV, KW, VR, DH, SS are shareholders of Novartis.

<!-- TABLE_START -->
### pop-PK parameters inferred from mice for DYP688

| Parameter | Notation (unit) | Estimate | RSE (%) | IISD | IISD RSE (%) |
| --- | --- | --- | --- | --- | --- |
| Antibody/ADC elimination rate | kel (1/h) | 0.016 | 12.1 | 0.45 | 28.6 |
| Plasma to periphery distribution rate | k12 (1/h) | 0.035 | 11.4 | 0.15 | - |
| Periphery to plasma distribution rare | k21 (1/h) | 0.036 | 14.1 | 0.26 | - |
| Payload inactivation rate | ki.a. (1/h) | 0.013 | 2.77 | 0.075 | 38.6 |
| Plasma volume of distribution | Vc (mL) | 1.53 | 2.5 | 0.03 | - |
| Fraction ADC with 1 active payload detected in active assay | pinact | 0.79 | 9.33 | - | - |
| Fraction ADC with 1 inactive payload detected in inactive assay | pinact2 | 0.16 | 24.5 | - | - |
| Fraction of inactive ADC in dose | fr | 0.027 | 0.46 | - | - |
| Additive lognormal measurement error model parameter for Total mAb | a (nM) | 0.41 | 8.67 | - | - |
| Additive lognormal measurement error model parameter for Active ADC | a (nM) | 0.24 | 7.73 | - | - |
| Additive lognormal measurement error model parameter for Inactive ADC | a (nM) | 0.52 | 8.24 | - | - |
| Additive and multiplicative normal measurement error model parameter for % conjugated payload | a (%), b | 0.65, 0.21 | 28.5, 10.3 | - | - |

<!-- TABLE_START -->
### pop-PK parameters inferred from NHP for DYP688

| Parameter (unit) | Notation (unit) | Estimate | RSE (%) | IISD | IISD RSE (%) |
| --- | --- | --- | --- | --- | --- |
| Plasma to periphery distribution rate | k12 (1/h) | 0.064 | 0.288 | - | - |
| Periphery to plasma distribution rate | k21 (1/h) | 0.039 | 11.8 | 0.48 | - |
| Payload inactivation rate | ki.a. (1/h) | 0.0114 | 1.62 | 0.06 | 22.1 |
| Antibody/ADC elimination rate | kel (1/h) | 0.013 | - | - | - |
| Plasma volume of distribution | Vc (L) | 0.083 | 4.83 | 0.21 |  |
| Multiplicative measurement error parameter for Total mAb | b | 0.37 | - |  |  |
| Multiplicative measurement error parameter for conj. active payload | b | 0.27 | 5.51 |  |  |
| Multiplicative measurement error parameter for conj. inactive payload | b | 0.73 | 4.26 |  |  |

<!-- TABLE_START -->
### 92.1 CDX tumor growth inhibition model parameters inferred from mice

| Parameter | Notation (unit) | Estimate | RSE (%) | IISD | IISD RSE (%) |
| --- | --- | --- | --- | --- | --- |
| 92.1 tumor growth parameters estimated from untreated tumor data |
| Tumor growth rate | β (1/h) | 0.0059 | 17.2 | 0.46 | 30.4 |
| Maximum tumor volume | TSmax (mm3) | 928.39 | 8.48 | 0.26 | 22.6 |
| Additive log normal measurement error | a (mm3) | 0.15 | 7.7 | - | - |
| TGI model parameters estimated from DYP688-treated groups in 92.1 CDX tumors |
| Tumor killing constant | kkill (1/nM) | 0.00016 | 47.4 | 0.9 | 19.4 |
| Dose-dependent kkill covariate (for 3 mg/kg group) | beta_kkill_3 | 0.72 | 75.3 | - | - |
| Dose-dependent kkill covariate (for 6 mg/kg group) | beta_kkill_6 | 1.18 | 51.7 | - | - |
| Transit compartment time | τ (h) | 698.87 | 0.0182 | 0.1 | - |
| Additive log-normal measurement error | a (mm3) | 0.73 | 3.35 | - | - |

<!-- TABLE_START -->
### Killing rate constant and tumorostatic concentration (TSC) inferred from 92.1 CDX TGI model

| Dose | Median killing rate constant (1/nM) | TSC (nM) |
| --- | --- | --- |
| 1.5 mg/kg | 0.00016 | 127 |
| 3 mg/kg | 0.00026 | 34 |
| 6 mg/kg | 0.00058 | 13.2 |

<!-- TABLE_START -->
### Allometric scaling of PK parameters from NHP to human

| Parameter | Estimated Cynomolgus PKMedian (IQR) | Predicted Human PKMedian (IQR) | Comments |
| --- | --- | --- | --- |
| Plasma volume, Vc (L) | 0.083(0.075–0.095) | 1.93(1.75–2.21) | \documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\:{V}_{human}={V}_{cyno}{\left(\frac{{BW}_{human}}{{BW}_{cyno}}\right)}^{w}$$\end{document}
w = 1a |
| Linear elimination, CL (L/hour) | 0.00108(0.00098–0.00124) | 0.0251(0.0228–0.0287) | \documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\:{CL}_{human}={CL}_{cyno}{\left(\frac{{BW}_{human}}{{BW}_{cyno}}\right)}^{w}$$\end{document}

\documentclass[12pt]{minimal}
				\usepackage{amsmath}
				\usepackage{wasysym} 
				\usepackage{amsfonts} 
				\usepackage{amssymb} 
				\usepackage{amsbsy}
				\usepackage{mathrsfs}
				\usepackage{upgreek}
				\setlength{\oddsidemargin}{-69pt}
				\begin{document}$$\:CL={V}_{c}\times\:\:{k}_{el}$$\end{document}
w = 1a |
| Plasma to peripheral distribution, k12 (1/hour) | 0.064(n/a) | 0.064(n/a) | Assumed same in cyno and human |
| Peripheral to plasma distribution, k21 (1/hour) | 0.039(0.03–0.046) | 0.039(0.03–0.046) | Assumed same in cyno and human |
| Initial fraction of inactive ADC, fr | 1% | 1% | Assumed same in cyno and human |
| Payload ring-opening inactivation, t1/2,inactivation (hours) | 61(59–63) | 61(59–63) | Assumed same in cyno and human based on in vitro stability data |