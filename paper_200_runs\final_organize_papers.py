#!/usr/bin/env python3
"""
Final script to organize research papers by document type.
Creates a clean folder with 400 papers categorized as either research or review.
"""

import pandas as pd
import os
import shutil
from pathlib import Path

def main():
    """Main function to create the final organized folder."""
    print("Creating final organized folder...")
    
    # Read Excel file to get document type mapping
    excel_path = 'xml_extraction_analysis_2.xlsx'
    excel_file = pd.ExcelFile(excel_path)
    
    # Read both sheets
    reviews_df = pd.read_excel(excel_path, sheet_name='reviews_analysis')
    articles_df = pd.read_excel(excel_path, sheet_name='articles_analysis')
    
    # Extract workids from each sheet
    review_workids = set()
    research_workids = set()
    
    # Get review workids
    for _, row in reviews_df.iterrows():
        workid = str(row['oa_works_id']).strip()
        if workid.startswith('W') and len(workid) > 5 and workid[1:].isdigit():
            review_workids.add(workid)
    
    # Get research workids
    for _, row in articles_df.iterrows():
        workid = str(row['oa_works_id']).strip()
        if workid.startswith('W') and len(workid) > 5 and workid[1:].isdigit():
            research_workids.add(workid)
    
    print(f"Found {len(review_workids)} review papers")
    print(f"Found {len(research_workids)} research papers")
    
    # Find overlaps (papers that appear in both categories)
    overlaps = review_workids.intersection(research_workids)
    print(f"Found {len(overlaps)} papers that appear in both categories")
    
    # Create final mapping: prioritize review classification
    final_mapping = {}
    
    # Add all review papers
    for workid in review_workids:
        final_mapping[workid] = 'review'
        final_mapping[workid.lower()] = 'review'  # Handle case sensitivity
    
    # Add research papers that are not already classified as reviews
    for workid in research_workids:
        if workid not in review_workids:
            final_mapping[workid] = 'research'
            final_mapping[workid.lower()] = 'research'  # Handle case sensitivity
    
    print(f"Final mapping has {len(set(final_mapping.values()))} unique document types")
    
    # Get all paper files
    papers = {}
    
    # Get files from mds_200 directory
    mds_200_path = Path('mds_200')
    if mds_200_path.exists():
        for file in mds_200_path.glob('*.md'):
            workid = file.name.replace('.md', '')
            papers[workid] = file
    
    # Get files from next_200_mds directory
    next_200_path = Path('next_200_mds')
    if next_200_path.exists():
        for file in next_200_path.glob('*.md'):
            workid = file.name.replace('.md', '')
            papers[workid] = file
    
    print(f"Found {len(papers)} total paper files")
    
    # Create clean output directory
    output_dir = Path('papers_by_type_final')
    if output_dir.exists():
        shutil.rmtree(output_dir)
    output_dir.mkdir()
    
    # Copy and rename files
    research_count = 0
    review_count = 0
    missing_count = 0
    
    for workid, source_path in papers.items():
        # Get document type from mapping
        doc_type = final_mapping.get(workid, final_mapping.get(workid.lower(), None))
        
        if doc_type is None:
            doc_type = 'research'  # Default to research
            missing_count += 1
        
        # Count by type
        if doc_type == 'research':
            research_count += 1
        elif doc_type == 'review':
            review_count += 1
        
        # Create new filename
        new_filename = f"{workid}_{doc_type}.md"
        dest_path = output_dir / new_filename
        
        # Copy file
        try:
            shutil.copy2(source_path, dest_path)
        except Exception as e:
            print(f"Error copying {source_path}: {e}")
    
    print(f"\nFinal Results:")
    print(f"Total files organized: {len(papers)}")
    print(f"Research papers: {research_count}")
    print(f"Review papers: {review_count}")
    print(f"Papers with missing mapping (defaulted to research): {missing_count}")
    print(f"Output directory: {output_dir}")
    
    # Verify no duplicates
    final_files = list(output_dir.glob('*.md'))
    print(f"Final folder contains {len(final_files)} files")
    
    # Show some examples
    print(f"\nFirst 10 files:")
    for i, file in enumerate(sorted(final_files)[:10]):
        print(f"  {file.name}")

if __name__ == "__main__":
    main()
