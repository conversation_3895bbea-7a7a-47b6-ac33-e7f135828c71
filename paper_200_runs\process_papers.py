#!/usr/bin/env python3
"""
<PERSON><PERSON>t to process research papers and categorize them by document type.
Creates a new folder with renamed files based on workid and document type.
"""

import pandas as pd
import os
import shutil
from pathlib import Path

def read_excel_file(excel_path):
    """Read the Excel file and extract document type information."""
    try:
        # Check if there are multiple sheets
        excel_file = pd.ExcelFile(excel_path)
        print(f"Excel file has {len(excel_file.sheet_names)} sheets: {excel_file.sheet_names}")

        # Try reading all sheets
        all_data = {}
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(excel_path, sheet_name=sheet_name)
            all_data[sheet_name] = df
            print(f"\nSheet '{sheet_name}': {len(df)} rows, {len(df.columns)} columns")
            print(f"Columns: {df.columns.tolist()}")

            # Look for rows that contain actual workids (starting with W followed by numbers)
            workid_rows = []
            for i, row in df.iterrows():
                if 'oa_works_id' in df.columns:
                    workid = str(row['oa_works_id']).strip()
                    if workid.startswith('W') and len(workid) > 5 and workid[1:].isdigit():
                        workid_rows.append(i)
                        if len(workid_rows) <= 5:  # Show first 5 actual data rows
                            content_type = row.get('content_type', 'N/A')
                            print(f"  Row {i}: workid={workid}, content_type={content_type}")

            print(f"  Found {len(workid_rows)} rows with actual workids")

            if workid_rows:
                # Filter to only the rows with actual data
                df_filtered = df.iloc[workid_rows].copy()
                print(f"  Filtered dataset has {len(df_filtered)} rows")
                all_data[sheet_name] = df_filtered

        # Return all sheets data for processing
        return all_data

    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return None

def extract_workid_from_filename(filename):
    """Extract workid from filename (remove .md extension)."""
    return filename.replace('.md', '').replace('.xml', '')

def get_all_paper_files():
    """Get all markdown files from both directories."""
    papers = {}
    
    # Get files from mds_200 directory
    mds_200_path = Path('mds_200')
    if mds_200_path.exists():
        for file in mds_200_path.glob('*.md'):
            workid = extract_workid_from_filename(file.name)
            papers[workid] = {
                'source_path': file,
                'source_dir': 'mds_200'
            }
        print(f"Found {len([f for f in mds_200_path.glob('*.md')])} files in mds_200")
    
    # Get files from next_200_mds directory
    next_200_path = Path('next_200_mds')
    if next_200_path.exists():
        for file in next_200_path.glob('*.md'):
            workid = extract_workid_from_filename(file.name)
            papers[workid] = {
                'source_path': file,
                'source_dir': 'next_200_mds'
            }
        print(f"Found {len([f for f in next_200_path.glob('*.md')])} files in next_200_mds")
    
    print(f"Total papers found: {len(papers)}")
    return papers

def create_document_type_mapping(all_sheets_data, papers):
    """Create mapping between workid and document type using both sheets."""
    mapping = {}

    print("\nAnalyzing Excel data from both sheets...")

    # Process each sheet
    for sheet_name, df in all_sheets_data.items():
        if 'oa_works_id' not in df.columns:
            continue

        print(f"\nProcessing sheet: {sheet_name}")

        # Determine document type based on sheet name
        if 'review' in sheet_name.lower():
            sheet_doc_type = 'review'
        elif 'article' in sheet_name.lower():
            sheet_doc_type = 'research'
        else:
            sheet_doc_type = 'research'  # Default

        print(f"Sheet '{sheet_name}' -> document type: {sheet_doc_type}")

        # Process each row in the sheet
        count = 0
        for _, row in df.iterrows():
            workid = str(row['oa_works_id']).strip()

            # Skip if not a valid workid
            if not (workid.startswith('W') and len(workid) > 5 and workid[1:].isdigit()):
                continue

            # Use the sheet-based document type, store both upper and lower case versions
            mapping[workid] = sheet_doc_type
            mapping[workid.lower()] = sheet_doc_type  # Handle case sensitivity
            count += 1

            if count <= 5:  # Show first 5 for verification
                content_type = row.get('content_type', 'N/A')
                print(f"  {workid} -> {sheet_doc_type} (content: {content_type})")

        print(f"Added {count} entries from {sheet_name}")

    print(f"\nTotal mapping created for {len(mapping)} entries")

    # Show distribution of document types
    type_counts = {}
    for doc_type in mapping.values():
        type_counts[doc_type] = type_counts.get(doc_type, 0) + 1
    print(f"Document type distribution: {type_counts}")

    return mapping

def create_organized_folder(papers, mapping):
    """Create a new folder with renamed files based on document type."""
    output_dir = Path('papers_by_type')
    output_dir.mkdir(exist_ok=True)

    copied_files = 0
    missing_mappings = []
    research_count = 0
    review_count = 0

    for workid, paper_info in papers.items():
        source_path = paper_info['source_path']

        # Get document type from mapping (try both original case and lowercase)
        doc_type = mapping.get(workid, mapping.get(workid.lower(), 'research'))

        if doc_type not in ['research', 'review']:
            missing_mappings.append(workid)
            doc_type = 'research'  # Default to research

        # Count document types
        if doc_type == 'research':
            research_count += 1
        elif doc_type == 'review':
            review_count += 1

        # Create new filename with workid_documenttype format
        new_filename = f"{workid}_{doc_type}.md"
        dest_path = output_dir / new_filename

        # Copy the file
        try:
            shutil.copy2(source_path, dest_path)
            copied_files += 1
            if copied_files <= 10:  # Show first 10 for verification
                print(f"Copied: {source_path} -> {dest_path}")
        except Exception as e:
            print(f"Error copying {source_path}: {e}")

    print(f"\nSummary:")
    print(f"Total files copied: {copied_files}")
    print(f"Research papers: {research_count}")
    print(f"Review papers: {review_count}")
    print(f"Files with missing document type mapping: {len(missing_mappings)}")

    if missing_mappings:
        print(f"Missing mappings: {missing_mappings}")

    return copied_files, missing_mappings

def main():
    """Main function to process papers and create organized folder."""
    print("Starting paper processing...")

    # Read Excel file
    excel_path = 'xml_extraction_analysis_2.xlsx'
    all_sheets_data = read_excel_file(excel_path)

    if all_sheets_data is None:
        print("Failed to read Excel file. Exiting.")
        return

    # Get all paper files
    papers = get_all_paper_files()

    if not papers:
        print("No paper files found. Exiting.")
        return

    # Create document type mapping
    mapping = create_document_type_mapping(all_sheets_data, papers)

    if not mapping:
        print("Failed to create document type mapping. Exiting.")
        return

    # Create organized folder
    copied_files, missing_mappings = create_organized_folder(papers, mapping)

    print(f"\nProcessing complete!")
    print(f"Created 'papers_by_type' folder with {copied_files} organized files.")

if __name__ == "__main__":
    main()
